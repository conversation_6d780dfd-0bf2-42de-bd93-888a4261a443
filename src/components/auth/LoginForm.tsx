'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { LoginData, LoginFormState } from '@/types/auth';
import { useTranslations, useLocale } from 'next-intl';
import { supabase } from '@/lib/supabase';

export function LoginForm() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations('Auth.Login');

  const [formState, setFormState] = useState<LoginFormState>({
    loading: false,
    error: null,
  });

  const [formData, setFormData] = useState<LoginData>({
    email: '',
    password: '',
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const handleInputChange = (field: keyof LoginData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      errors.email = t('validation.emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = t('validation.emailInvalid');
    }

    // Password validation
    if (!formData.password) {
      errors.password = t('validation.passwordRequired');
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setFormState({ loading: true, error: null });

    try {
      // Sign in with Supabase Auth directly
      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email: formData.email,
          password: formData.password,
        });

      if (authError) {
        console.error('Auth error:', authError);

        // Handle specific error cases
        if (authError.message.includes('Invalid login credentials')) {
          setFormState({
            loading: false,
            error: t('errors.invalidCredentials'),
          });
        } else if (authError.message.includes('Email not confirmed')) {
          setFormState({
            loading: false,
            error: t('errors.emailNotVerified'),
          });
        } else {
          setFormState({
            loading: false,
            error: authError.message,
          });
        }
        return;
      }

      if (!authData.user) {
        setFormState({
          loading: false,
          error: t('errors.loginFailed'),
        });
        return;
      }

      // Redirect to dashboard on successful login
      router.push(`/${locale}/dashboard`);

    } catch (error) {
      console.error('Login error:', error);
      setFormState({
        loading: false,
        error: t('errors.networkError'),
      });
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setValidationErrors({ email: t('validation.emailRequiredForReset') });
      return;
    }

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(
        formData.email,
        {
          redirectTo: `${window.location.origin}/${locale}/auth/reset-password`,
        },
      );

      if (error) {
        setFormState({
          loading: false,
          error: error.message,
        });
      } else {
        setFormState({
          loading: false,
          error: null,
        });
        alert(t('passwordReset.emailSent'));
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setFormState({
        loading: false,
        error: t('errors.networkError'),
      });
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100">
            <svg
              className="h-6 w-6 text-indigo-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {t('title')}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {t('subtitle')}{' '}
            <Link
              href={`/${locale}/auth/signup`}
              className="font-medium text-indigo-600 hover:text-indigo-500"
            >
              {t('signupLink')}
            </Link>
          </p>
        </div>

        {/* Error display */}
        {formState.error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Login Error
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{formState.error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                {t('fields.email.label')}
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.email ? 'border-red-300' : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('fields.email.placeholder')}
              />
              {validationErrors.email && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                {t('fields.password.label')}
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`relative mt-1 block w-full appearance-none border px-3 py-2 ${
                  validationErrors.password
                    ? 'border-red-300'
                    : 'border-gray-300'
                } rounded-md text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                placeholder={t('fields.password.placeholder')}
              />
              {validationErrors.password && (
                <p className="mt-1 text-sm text-red-600">
                  {validationErrors.password}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                {t('forgotPassword')}
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={formState.loading}
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            >
              {formState.loading ? t('signingIn') : t('signIn')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
